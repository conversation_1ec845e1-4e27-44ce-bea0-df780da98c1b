<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>修复版爱心烟花游戏</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      font-family: 'Microsoft YaHei', sans-serif;
      background: linear-gradient(135deg, #0b1020 0%, #1a1f3b 100%);
    }

    .instructions {
      position: absolute;
      bottom: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 20px;
      border-radius: 15px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      font-size: 14px;
      z-index: 1000;
      width: 300px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    }

    .instructions h3 {
      margin: 0 0 15px 0;
      color: #ff69b4;
      font-weight: bold;
      text-align: center;
      font-size: 18px;
    }

    .control-item {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 8px;
      padding: 5px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 5px;
    }

    .key {
      background: linear-gradient(135deg, #ff6ec7, #ff9a9e);
      color: white;
      padding: 3px 8px;
      border-radius: 4px;
      font-weight: bold;
      font-size: 12px;
      min-width: 30px;
      text-align: center;
    }

    .info {
      font-size: 12px;
      color: #ddd;
      line-height: 1.4;
      margin-top: 15px;
      padding: 10px;
      background: rgba(255, 105, 180, 0.1);
      border-radius: 8px;
      border-left: 3px solid #ff69b4;
    }

    .loading {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 24px;
      z-index: 2000;
      text-align: center;
    }

    .loading .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid #ff69b4;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .version {
      position: absolute;
      bottom: 10px;
      left: 10px;
      color: rgba(255, 255, 255, 0.5);
      font-size: 10px;
      z-index: 1000;
    }

    .error-display {
      position: fixed;
      top: 10px;
      left: 10px;
      background: rgba(255, 0, 0, 0.9);
      color: white;
      padding: 15px;
      border-radius: 8px;
      z-index: 3000;
      max-width: 400px;
      display: none;
      font-family: monospace;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="loading" id="loading">
    <div class="spinner"></div>
    <div>加载修复版爱心烟花游戏...</div>
  </div>

  <div class="error-display" id="errorDisplay"></div>

  <div class="instructions" id="instructions" style="display: none;">
    <h3>💗 修复版爱心烟花游戏 💗</h3>

    <div class="control-item">
      <span class="key">鼠标左键</span>
      <span>点击气球射击</span>
    </div>
    <div class="control-item">
      <span class="key">左键双击</span>
      <span>爆炸攻击（范围伤害）</span>
    </div>
    <div class="control-item">
      <span class="key">空格键</span>
      <span>清除所有粒子</span>
    </div>
    <div class="control-item">
      <span class="key">R键</span>
      <span>重新开始游戏</span>
    </div>

    <div class="info">
      <strong style="color: #ff69b4;">🎯 游戏目标：</strong>
      <span style="color: #87ceeb;">点击气球获得分数，完成关卡目标！</span><br>
      <strong style="color: #ff69b4;">💥 特殊技能：</strong>
      <span style="color: #87ceeb;">快速双击触发爆炸攻击，清理大片气球！</span><br>
      <strong style="color: #ff69b4;">🏆 连击系统：</strong>
      <span style="color: #87ceeb;">连续击中气球可获得连击分数加成！</span>
    </div>
  </div>

  <div class="version">Fixed v1.0</div>

  <script>
    // 错误处理
    let errorDisplay = document.getElementById('errorDisplay');
    let hasError = false;

    window.onerror = function(msg, url, line, col, error) {
      hasError = true;
      errorDisplay.innerHTML = `
        <strong>游戏错误:</strong><br>
        ${msg}<br>
        <small>文件: ${url}<br>行号: ${line}</small>
      `;
      errorDisplay.style.display = 'block';
      document.getElementById('loading').style.display = 'none';
      return false;
    };

    // 页面加载完成后隐藏加载界面
    window.addEventListener('load', function() {
      setTimeout(() => {
        if (!hasError) {
          document.getElementById('loading').style.display = 'none';
          document.getElementById('instructions').style.display = 'block';
        }
      }, 1500);
    });

    // 说明面板切换
    let instructionsVisible = true;
    document.addEventListener('keydown', function(e) {
      if (e.key === 'i' || e.key === 'I') {
        const instructions = document.getElementById('instructions');
        instructionsVisible = !instructionsVisible;
        instructions.style.display = instructionsVisible ? 'block' : 'none';
      }
    });
  </script>
  
  <script src="fixed_game.js"></script>
</body>
</html>
