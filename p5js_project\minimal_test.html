<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>最小化测试</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      background: #000;
      overflow: hidden;
    }
    #status {
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(0, 255, 0, 0.8);
      color: black;
      padding: 10px;
      border-radius: 5px;
      z-index: 1000;
      font-family: Arial, sans-serif;
    }
    #error {
      position: fixed;
      bottom: 10px;
      left: 10px;
      background: rgba(255, 0, 0, 0.8);
      color: white;
      padding: 10px;
      border-radius: 5px;
      z-index: 1000;
      font-family: Arial, sans-serif;
      display: none;
      max-width: 400px;
    }
  </style>
</head>
<body>
  <div id="status">最小化测试运行中</div>
  <div id="error"></div>

  <script>
    let errorDiv = document.getElementById('error');
    let statusDiv = document.getElementById('status');
    
    // 错误捕获
    window.onerror = function(msg, url, line, col, error) {
      errorDiv.innerHTML = `错误: ${msg}<br>行号: ${line}`;
      errorDiv.style.display = 'block';
      statusDiv.innerHTML = '发现错误';
      statusDiv.style.background = 'rgba(255, 0, 0, 0.8)';
      statusDiv.style.color = 'white';
      return false;
    };
    
    // 加载测试脚本
    setTimeout(() => {
      let script = document.createElement('script');
      script.src = 'minimal_test.js';
      script.onload = function() {
        statusDiv.innerHTML = '最小化测试成功';
      };
      script.onerror = function() {
        errorDiv.innerHTML = '无法加载测试脚本';
        errorDiv.style.display = 'block';
      };
      document.head.appendChild(script);
    }, 500);
  </script>
</body>
</html>
