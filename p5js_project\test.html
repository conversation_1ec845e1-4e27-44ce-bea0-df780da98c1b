<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>游戏测试</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.js"></script>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background: #222;
      color: white;
    }
    .test-info {
      position: fixed;
      top: 10px;
      left: 10px;
      background: rgba(0,0,0,0.8);
      padding: 10px;
      border-radius: 5px;
      z-index: 1000;
    }
    .error {
      color: #ff6666;
    }
    .success {
      color: #66ff66;
    }
  </style>
</head>
<body>
  <div class="test-info" id="testInfo">
    <div>游戏测试状态：<span id="status">检测中...</span></div>
    <div>错误信息：<span id="errors">无</span></div>
  </div>

  <script>
    // 错误捕获
    let errors = [];
    let gameLoaded = false;
    
    window.onerror = function(msg, url, line, col, error) {
      errors.push(`${msg} (行 ${line})`);
      updateStatus();
      return false;
    };
    
    window.addEventListener('unhandledrejection', function(event) {
      errors.push(`Promise错误: ${event.reason}`);
      updateStatus();
    });
    
    function updateStatus() {
      const statusEl = document.getElementById('status');
      const errorsEl = document.getElementById('errors');
      
      if (errors.length > 0) {
        statusEl.innerHTML = '<span class="error">发现错误</span>';
        errorsEl.innerHTML = '<span class="error">' + errors.join('<br>') + '</span>';
      } else if (gameLoaded) {
        statusEl.innerHTML = '<span class="success">游戏正常运行</span>';
        errorsEl.innerHTML = '<span class="success">无错误</span>';
      } else {
        statusEl.innerHTML = '加载中...';
        errorsEl.innerHTML = '检测中...';
      }
    }
    
    // 简单的p5.js测试
    function setup() {
      createCanvas(400, 300);
      gameLoaded = true;
      updateStatus();
      console.log('p5.js setup完成');
    }
    
    function draw() {
      background(50);
      fill(255);
      textAlign(CENTER, CENTER);
      text('p5.js 正常工作', width/2, height/2);
      
      // 测试一些基本功能
      if (frameCount === 60) { // 1秒后测试
        try {
          // 测试向量
          let v = createVector(1, 2);
          // 测试颜色
          let c = color(255, 0, 0);
          // 测试随机数
          let r = random(100);
          console.log('基本功能测试通过');
        } catch (e) {
          errors.push('基本功能测试失败: ' + e.message);
          updateStatus();
        }
      }
    }
    
    // 延迟加载主游戏脚本
    setTimeout(() => {
      const script = document.createElement('script');
      script.src = 'sketch.js';
      script.onerror = function() {
        errors.push('无法加载 sketch.js');
        updateStatus();
      };
      document.head.appendChild(script);
    }, 1000);
  </script>
</body>
</html>
