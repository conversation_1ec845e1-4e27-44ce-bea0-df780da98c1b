<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简单测试</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      background: #000;
      color: white;
      font-family: Arial, sans-serif;
    }
    #error-log {
      position: fixed;
      top: 10px;
      left: 10px;
      background: rgba(255, 0, 0, 0.8);
      padding: 10px;
      border-radius: 5px;
      max-width: 400px;
      max-height: 200px;
      overflow-y: auto;
      z-index: 1000;
      display: none;
    }
    #success-log {
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(0, 255, 0, 0.8);
      padding: 10px;
      border-radius: 5px;
      z-index: 1000;
      color: black;
    }
  </style>
</head>
<body>
  <div id="error-log"></div>
  <div id="success-log">游戏加载中...</div>

  <script>
    let errorLog = document.getElementById('error-log');
    let successLog = document.getElementById('success-log');
    let errors = [];
    
    // 捕获所有错误
    window.onerror = function(msg, url, line, col, error) {
      let errorMsg = `错误: ${msg}\n文件: ${url}\n行号: ${line}\n列号: ${col}`;
      if (error && error.stack) {
        errorMsg += `\n堆栈: ${error.stack}`;
      }
      errors.push(errorMsg);
      showErrors();
      return false;
    };
    
    window.addEventListener('unhandledrejection', function(event) {
      errors.push(`Promise错误: ${event.reason}`);
      showErrors();
    });
    
    function showErrors() {
      if (errors.length > 0) {
        errorLog.innerHTML = errors.join('<hr>');
        errorLog.style.display = 'block';
        successLog.style.display = 'none';
      }
    }
    
    // 测试基本的p5.js功能
    let testPassed = false;
    
    function setup() {
      try {
        createCanvas(windowWidth, windowHeight);
        successLog.innerHTML = 'p5.js setup 成功';
        testPassed = true;
        
        // 延迟加载主游戏
        setTimeout(loadMainGame, 1000);
      } catch (e) {
        errors.push(`Setup错误: ${e.message}`);
        showErrors();
      }
    }
    
    function draw() {
      if (!testPassed) return;
      
      try {
        background(20, 20, 40);
        fill(255);
        textAlign(CENTER, CENTER);
        textSize(24);
        text('游戏测试中...', width/2, height/2);
        
        if (frameCount < 60) {
          text(`加载进度: ${Math.floor(frameCount/60*100)}%`, width/2, height/2 + 50);
        }
      } catch (e) {
        errors.push(`Draw错误: ${e.message}`);
        showErrors();
      }
    }
    
    function loadMainGame() {
      try {
        successLog.innerHTML = '开始加载主游戏...';
        
        // 创建script标签加载主游戏
        let script = document.createElement('script');
        script.src = 'sketch.js';
        script.onload = function() {
          successLog.innerHTML = '主游戏加载成功！';
          setTimeout(() => {
            successLog.style.display = 'none';
          }, 3000);
        };
        script.onerror = function() {
          errors.push('无法加载 sketch.js 文件');
          showErrors();
        };
        
        document.head.appendChild(script);
      } catch (e) {
        errors.push(`加载主游戏错误: ${e.message}`);
        showErrors();
      }
    }
    
    function windowResized() {
      if (testPassed) {
        resizeCanvas(windowWidth, windowHeight);
      }
    }
  </script>
</body>
</html>
