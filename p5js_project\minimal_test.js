// 最小化测试版本 - 只包含核心功能

// 全局变量
let particles = [];
let balloons = [];
let score = 0;
let gameState = "playing";

// 简单的粒子类
class SimpleParticle {
  constructor(x, y) {
    this.x = x;
    this.y = y;
    this.vx = random(-3, 3);
    this.vy = random(-3, 3);
    this.life = 255;
  }
  
  update() {
    this.x += this.vx;
    this.y += this.vy;
    this.life -= 3;
  }
  
  show() {
    fill(255, 100, 150, this.life);
    noStroke();
    ellipse(this.x, this.y, 5);
  }
  
  isDead() {
    return this.life <= 0;
  }
}

// 简单的气球类
class SimpleBalloon {
  constructor() {
    this.x = random(50, width - 50);
    this.y = height + 50;
    this.vy = random(-1, -2);
    this.r = random(20, 40);
    this.color = color(random(255), random(255), random(255));
  }
  
  update() {
    this.y += this.vy;
  }
  
  show() {
    fill(this.color);
    stroke(255);
    strokeWeight(2);
    ellipse(this.x, this.y, this.r * 2);
  }
  
  isOffScreen() {
    return this.y < -this.r;
  }
  
  isClicked(mx, my) {
    return dist(mx, my, this.x, this.y) < this.r;
  }
}

function setup() {
  createCanvas(windowWidth, windowHeight);
  console.log("Setup完成");
}

function draw() {
  background(20, 20, 40);
  
  // 生成气球
  if (frameCount % 120 === 0 && balloons.length < 5) {
    balloons.push(new SimpleBalloon());
  }
  
  // 更新和绘制气球
  for (let i = balloons.length - 1; i >= 0; i--) {
    balloons[i].update();
    balloons[i].show();
    
    if (balloons[i].isOffScreen()) {
      balloons.splice(i, 1);
    }
  }
  
  // 更新和绘制粒子
  for (let i = particles.length - 1; i >= 0; i--) {
    particles[i].update();
    particles[i].show();
    
    if (particles[i].isDead()) {
      particles.splice(i, 1);
    }
  }
  
  // 显示分数
  fill(255);
  textAlign(LEFT, TOP);
  textSize(20);
  text("分数: " + score, 20, 20);
  text("气球: " + balloons.length, 20, 50);
  text("粒子: " + particles.length, 20, 80);
  
  // 显示帧率
  text("FPS: " + Math.round(frameRate()), 20, 110);
}

function mousePressed() {
  console.log("鼠标点击:", mouseX, mouseY);
  
  let hit = false;
  
  // 检查是否击中气球
  for (let i = balloons.length - 1; i >= 0; i--) {
    if (balloons[i].isClicked(mouseX, mouseY)) {
      hit = true;
      score += 10;
      
      // 创建粒子效果
      for (let j = 0; j < 10; j++) {
        particles.push(new SimpleParticle(balloons[i].x, balloons[i].y));
      }
      
      balloons.splice(i, 1);
      break;
    }
  }
  
  if (!hit) {
    // 创建少量粒子
    for (let j = 0; j < 5; j++) {
      particles.push(new SimpleParticle(mouseX, mouseY));
    }
  }
}

function windowResized() {
  resizeCanvas(windowWidth, windowHeight);
}

console.log("最小化测试脚本加载完成");
