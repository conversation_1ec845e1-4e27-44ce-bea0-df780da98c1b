// 修复版爱心烟花游戏 - 简化但功能完整

// ============= 全局变量 =============
let particles = [];
let heartParticles = [];
let balloons = [];
let score = 0;
let combo = 0;
let comboTimer = 0;
let gameState = "playing";
let currentLevel = 1;
let levelTarget = 5;
let levelBalloonCount = 0;
let balloonsPoppedCount = 0;
let missCount = 0;

// 音频系统
let soundEnabled = true;
let audioContext;
let audioInitialized = false;

// 双击检测
let clickCount = 0;
let doubleClickTime = 0;
let doubleClickThreshold = 300;
let burstCooldown = 0;
let burstRadius = 100;

// 视觉效果
let cameraShakeX = 0;
let cameraShakeY = 0;
let flashOverlay = 0;

// ============= 粒子类 =============
class Particle {
  constructor(x, y, customColor = null) {
    this.pos = createVector(x, y);
    this.vel = p5.Vector.random2D().mult(random(1, 5));
    this.acc = createVector(0, 0);
    this.r = random(2, 6);
    this.lifetime = 255;
    this.maxLifetime = 255;
    this.color = customColor || color(random(255), random(255), random(255));
  }

  applyForce(force) {
    this.acc.add(force);
  }

  update() {
    this.vel.add(this.acc);
    this.pos.add(this.vel);
    this.acc.mult(0);
    this.lifetime -= 2;
    this.vel.mult(0.99);
  }

  show() {
    let alpha = map(this.lifetime, 0, this.maxLifetime, 0, 255);
    stroke(red(this.color), green(this.color), blue(this.color), alpha);
    strokeWeight(this.r);
    point(this.pos.x, this.pos.y);
  }

  isFinished() {
    return this.lifetime < 0;
  }
}

// 爱心粒子类
class HeartParticle extends Particle {
  constructor(x, y, customColor = null) {
    super(x, y, customColor);
    this.lifetime = 300;
    this.maxLifetime = 300;
    this.color = customColor || color(255, 105, 180);
  }

  show() {
    let alpha = map(this.lifetime, 0, this.maxLifetime, 0, 255);
    
    // 发光效果
    push();
    blendMode(ADD);
    stroke(red(this.color), green(this.color), blue(this.color), alpha * 0.3);
    strokeWeight(this.r * 1.5);
    point(this.pos.x, this.pos.y);
    pop();

    // 主粒子
    stroke(red(this.color), green(this.color), blue(this.color), alpha);
    strokeWeight(this.r);
    point(this.pos.x, this.pos.y);
  }
}

// 气球类
class Balloon {
  constructor(type = "normal") {
    this.pos = createVector(random(width), height + 50);
    this.vel = createVector(random(-0.5, 0.5), random(-1, -2));
    this.type = type;
    this.bobOffset = random(TWO_PI);
    this.bobSpeed = random(0.02, 0.05);
    this.hitFlash = 0;
    this.scale = 1;
    this.targetScale = 1;

    if (type === "special") {
      this.r = random(40, 60);
      this.color = color(255, 215, 0);
      this.points = 50;
      this.vel.mult(0.7);
    } else if (type === "boss") {
      this.r = 80;
      this.color = color(255, 0, 100);
      this.points = 100;
      this.vel.mult(0.5);
      this.health = 5;
      this.maxHealth = 5;
    } else {
      this.r = random(30, 50);
      this.color = color(random(255), random(255), random(255));
      this.points = 25;
    }
  }

  update() {
    this.pos.add(this.vel);
    this.pos.x += sin(millis() * this.bobSpeed + this.bobOffset) * 0.3;
    this.scale = lerp(this.scale, this.targetScale, 0.1);
    
    if (this.hitFlash > 0) {
      this.hitFlash -= 5;
    }
  }

  show() {
    push();
    translate(this.pos.x, this.pos.y);
    scale(this.scale);

    // 击中闪光效果
    if (this.hitFlash > 0) {
      push();
      blendMode(ADD);
      fill(255, 255, 255, this.hitFlash);
      noStroke();
      ellipse(0, 0, this.r * 2.5, this.r * 2.7);
      pop();
    }

    // 主气球体
    fill(this.color);
    stroke(255, 255, 255, 200);
    strokeWeight(2);
    ellipse(0, 0, this.r * 2, this.r * 2.2);

    // Boss血量显示
    if (this.type === "boss") {
      fill(255);
      noStroke();
      textAlign(CENTER, CENTER);
      textSize(16);
      text(this.health + "/" + this.maxHealth, 0, 0);
    }

    // 气球线
    if (this.type !== "boss") {
      stroke(100, 100, 100, 150);
      strokeWeight(1);
      line(0, this.r * 1.1, 0, this.r * 1.8);
    }

    pop();
  }

  isOffScreen() {
    return this.pos.y < -this.r;
  }

  isClicked(mx, my) {
    let d = dist(mx, my, this.pos.x, this.pos.y);
    return d < this.r;
  }

  onHit() {
    this.hitFlash = 100;
    this.targetScale = 1.3;
    setTimeout(() => { this.targetScale = 1; }, 100);
    return true;
  }
}

// ============= 音频系统 =============
function initAudio() {
  if (audioInitialized) return true;

  try {
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
    if (audioContext) {
      if (audioContext.state === 'suspended') {
        audioContext.resume();
      }
      audioInitialized = true;
      return true;
    }
  } catch (e) {
    console.warn('Web Audio API not supported:', e);
    audioContext = null;
  }
  return false;
}

function playClickSound(frequency = 800, duration = 0.1) {
  if (!soundEnabled || !audioContext) return;

  try {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = 'sine';
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
  } catch (e) {
    console.warn('Error playing sound:', e);
  }
}

function playPopSound(pitch = 1.0) {
  if (!soundEnabled || !audioContext) return;
  try {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(400 * pitch, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(800 * pitch, audioContext.currentTime + 0.1);
    oscillator.type = 'square';

    gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.15);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.15);
  } catch (e) {
    console.log('Error playing pop sound');
  }
}

// ============= 主要函数 =============
function setup() {
  createCanvas(windowWidth, windowHeight);
  
  // 初始化音频
  initAudio();
  
  // 监听用户交互来激活音频上下文
  const startAudio = () => {
    if (audioContext && audioContext.state === 'suspended') {
      audioContext.resume();
    }
    document.removeEventListener('click', startAudio);
    document.removeEventListener('touchstart', startAudio);
  };
  document.addEventListener('click', startAudio);
  document.addEventListener('touchstart', startAudio);
  
  console.log("游戏初始化完成");
}

function draw() {
  // 应用屏幕震动
  push();
  translate(cameraShakeX, cameraShakeY);

  // 背景
  background(20, 20, 40);

  // 游戏逻辑
  if (gameState === "playing") {
    updateGameplay();
  }

  // 更新和绘制所有对象
  updateAndDrawObjects();

  // 绘制UI
  drawUI();

  // 闪光效果
  if (flashOverlay > 0) {
    fill(255, 255, 255, flashOverlay);
    rect(0, 0, width, height);
    flashOverlay -= 10;
  }

  pop();

  // 更新效果
  updateEffects();
}

// ============= 游戏逻辑函数 =============
function updateGameplay() {
  // 生成气球
  if (frameCount % 120 === 0 && balloons.length < 8) {
    let balloonType = "normal";
    let rand = random();

    // Boss关卡（每5关）
    if (currentLevel % 5 === 0 && balloons.length === 0) {
      balloonType = "boss";
    } else if (rand < 0.1) {
      balloonType = "special";
    }

    balloons.push(new Balloon(balloonType));
  }
}

function updateAndDrawObjects() {
  let gravity = createVector(0, 0.05);

  // 更新和绘制气球
  for (let i = balloons.length - 1; i >= 0; i--) {
    balloons[i].update();
    balloons[i].show();
    if (balloons[i].isOffScreen()) {
      balloons.splice(i, 1);
    }
  }

  // 更新和绘制粒子
  for (let i = particles.length - 1; i >= 0; i--) {
    particles[i].applyForce(gravity);
    particles[i].update();
    particles[i].show();
    if (particles[i].isFinished()) {
      particles.splice(i, 1);
    }
  }

  // 更新和绘制爱心粒子
  for (let i = heartParticles.length - 1; i >= 0; i--) {
    heartParticles[i].applyForce(gravity);
    heartParticles[i].update();
    heartParticles[i].show();
    if (heartParticles[i].isFinished()) {
      heartParticles.splice(i, 1);
    }
  }
}

function drawUI() {
  // 左上角信息
  fill(255);
  textAlign(LEFT, TOP);
  textSize(20);
  text("分数: " + score, 20, 20);
  text("连击: " + combo, 20, 50);
  text("关卡: " + currentLevel, 20, 80);
  text("目标: " + levelBalloonCount + "/" + levelTarget, 20, 110);

  // 右上角统计
  textAlign(RIGHT, TOP);
  text("气球: " + balloons.length, width - 20, 20);
  text("粒子: " + (particles.length + heartParticles.length), width - 20, 50);
  text("FPS: " + Math.round(frameRate()), width - 20, 80);

  // 爆炸冷却提示
  if (burstCooldown > 0) {
    fill(255, 100, 100);
    textAlign(CENTER, TOP);
    text("爆炸冷却: " + Math.ceil(burstCooldown / 60) + "s", width/2, 20);
  } else {
    fill(100, 255, 100);
    textAlign(CENTER, TOP);
    text("双击触发爆炸攻击", width/2, 20);
  }
}

function updateEffects() {
  // 更新屏幕震动
  cameraShakeX *= 0.9;
  cameraShakeY *= 0.9;
  if (abs(cameraShakeX) < 0.1) cameraShakeX = 0;
  if (abs(cameraShakeY) < 0.1) cameraShakeY = 0;

  // 更新冷却时间
  if (burstCooldown > 0) {
    burstCooldown--;
  }

  // 连击计时器
  if (combo > 0 && millis() - comboTimer > 2000) {
    combo = 0;
  }
}

// ============= 交互函数 =============
function mousePressed() {
  if (gameState === "playing") {
    handleMouseClick();
  }
}

function handleMouseClick() {
  let currentTime = millis();

  // 双击检测
  if (currentTime - doubleClickTime < doubleClickThreshold && clickCount === 1) {
    // 检测到双击，触发爆炸能力
    if (gameState === "playing" && burstCooldown <= 0) {
      activateBurstAbility();
      clickCount = 0;
      return;
    }
  }

  // 重置或增加点击计数
  if (currentTime - doubleClickTime > doubleClickThreshold) {
    clickCount = 0;
  }
  clickCount++;
  doubleClickTime = currentTime;

  // 普通点击处理
  let hitSomething = false;

  // 检查气球点击
  for (let i = balloons.length - 1; i >= 0; i--) {
    if (balloons[i].isClicked(mouseX, mouseY)) {
      hitSomething = true;
      handleBalloonHit(balloons[i], i);
      break;
    }
  }

  // 如果没有击中任何物体
  if (!hitSomething) {
    handleMiss();
  } else {
    // 击中了某个物体，处理连击
    handleCombo();
  }
}

function handleBalloonHit(balloon, index) {
  balloon.onHit();

  if (balloon.type === "boss") {
    balloon.health--;

    playPopSound(0.8);
    addScreenShake(8);
    addFlash(50);

    // Boss受伤效果
    for (let j = 0; j < 10; j++) {
      particles.push(new Particle(balloon.pos.x, balloon.pos.y, color(255, 100, 100)));
    }

    score += 20;

    if (balloon.health <= 0) {
      // Boss被击败
      handleBossDefeat(balloon, index);
    }
  } else {
    // 普通气球被击中
    handleNormalBalloonPop(balloon, index);
  }
}

function handleBossDefeat(balloon, index) {
  playPopSound(1.5);
  addScreenShake(15);
  addFlash(150);

  let bonusScore = balloon.points + combo * 20;
  score += bonusScore;
  balloonsPoppedCount++;
  levelBalloonCount++;

  // Boss爆炸效果
  for (let j = 0; j < 30; j++) {
    heartParticles.push(new HeartParticle(balloon.pos.x, balloon.pos.y, color(255, 215, 0)));
  }

  for (let j = 0; j < 20; j++) {
    let p = new Particle(balloon.pos.x, balloon.pos.y, color(255, 0, 100));
    p.vel.mult(3);
    particles.push(p);
  }

  balloons.splice(index, 1);
  checkLevelComplete();
}

function handleNormalBalloonPop(balloon, index) {
  playPopSound(balloon.type === "special" ? 1.3 : 1.0);
  addScreenShake(balloon.type === "special" ? 8 : 5);

  let bonusScore = balloon.points + combo * 5;
  score += bonusScore;
  balloonsPoppedCount++;
  levelBalloonCount++;

  // 粒子效果
  let particleCount = balloon.type === "special" ? 20 : 10;
  for (let j = 0; j < particleCount; j++) {
    if (balloon.type === "special") {
      heartParticles.push(new HeartParticle(balloon.pos.x, balloon.pos.y, balloon.color));
    } else {
      particles.push(new Particle(balloon.pos.x, balloon.pos.y, balloon.color));
    }
  }

  balloons.splice(index, 1);
  checkLevelComplete();
}

function handleMiss() {
  missCount++;

  // 重置连击
  if (combo > 0) {
    combo = 0;
  }

  // 创建普通烟花效果
  for (let i = 0; i < 8; i++) {
    particles.push(new Particle(mouseX, mouseY));
  }
}

function handleCombo() {
  combo++;
  if (combo > 10) combo = 10;
  comboTimer = millis();
}

function activateBurstAbility() {
  burstCooldown = 300; // 5秒冷却

  playPopSound(0.5);
  addScreenShake(10);
  addFlash(120);

  // 爆炸效果
  for (let i = 0; i < 50; i++) {
    let p = new HeartParticle(mouseX, mouseY, color(255, 100, 100));
    p.vel.mult(2);
    heartParticles.push(p);
  }

  // 检查爆炸范围内的气球
  for (let i = balloons.length - 1; i >= 0; i--) {
    let d = dist(mouseX, mouseY, balloons[i].pos.x, balloons[i].pos.y);
    if (d < burstRadius) {
      handleBalloonHit(balloons[i], i);
    }
  }
}

function checkLevelComplete() {
  if (levelBalloonCount >= levelTarget) {
    currentLevel++;
    levelBalloonCount = 0;
    levelTarget = 5 + currentLevel * 2; // 逐渐增加目标

    // 关卡完成效果
    for (let i = 0; i < 100; i++) {
      heartParticles.push(new HeartParticle(random(width), random(height), color(255, 215, 0)));
    }

    addScreenShake(20);
    addFlash(200);
  }
}

// ============= 辅助函数 =============
function addScreenShake(intensity = 5) {
  cameraShakeX += random(-intensity, intensity);
  cameraShakeY += random(-intensity, intensity);
}

function addFlash(intensity = 100) {
  flashOverlay = intensity;
}

function keyPressed() {
  if (key === ' ') {
    // 空格键清除粒子
    particles = [];
    heartParticles = [];
  } else if (key === 'r' || key === 'R') {
    // R键重新开始
    restartGame();
  }
}

function restartGame() {
  score = 0;
  combo = 0;
  currentLevel = 1;
  levelTarget = 5;
  levelBalloonCount = 0;
  balloonsPoppedCount = 0;
  missCount = 0;
  particles = [];
  heartParticles = [];
  balloons = [];
  burstCooldown = 0;
  gameState = "playing";
}

function windowResized() {
  resizeCanvas(windowWidth, windowHeight);
}

console.log("修复版游戏脚本加载完成");
