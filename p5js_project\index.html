<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Enhanced Heart Fireworks Game - 增强版爱心烟花游戏</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      font-family: 'Poppins', 'Microsoft YaHei', sans-serif;
      background: linear-gradient(135deg, #0b1020 0%, #1a1f3b 100%);
    }

    .instructions {
      position: absolute;
      bottom: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.92);
      backdrop-filter: blur(15px);
      color: white;
      padding: 20px;
      border-radius: 18px;
      border: 1px solid rgba(255, 255, 255, 0.15);
      font-size: 13px;
      z-index: 1000;
      width: 350px;
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      opacity: 0.95;
      transform: translateY(0);
    }

    .instructions:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    }

    .instructions h3 {
      margin: 0 0 18px 0;
      color: #ff69b4;
      font-weight: 700;
      text-align: center;
      font-size: 18px;
      text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
      background: linear-gradient(135deg, #ff69b4, #ff9a9e);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .section {
      margin-bottom: 16px;
    }

    .section h4 {
      color: #00e5ff;
      font-size: 14px;
      margin-bottom: 10px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .section h4::before {
      content: '';
      width: 4px;
      height: 16px;
      background: linear-gradient(135deg, #00e5ff, #0099cc);
      border-radius: 2px;
    }

    .controls-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      margin-bottom: 12px;
    }

    .control-item {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 6px 8px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.2s ease;
    }

    .control-item:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-1px);
    }

    .key {
      background: linear-gradient(135deg, #ff6ec7, #ff9a9e);
      color: white;
      padding: 3px 8px;
      border-radius: 6px;
      font-weight: 600;
      font-size: 11px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      min-width: 24px;
      text-align: center;
    }

    .feature-list {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 6px;
      margin: 12px 0;
    }

    .feature-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 11px;
      color: #ccc;
      padding: 4px 0;
    }

    .feature-icon {
      font-size: 14px;
      width: 16px;
      text-align: center;
    }

    .info {
      font-size: 12px;
      color: #ddd;
      line-height: 1.5;
      margin-top: 12px;
      padding: 12px;
      background: rgba(255, 105, 180, 0.1);
      border-radius: 10px;
      border-left: 4px solid #ff69b4;
    }

    .hide-tip {
      text-align: center;
      margin-top: 12px;
      padding: 8px;
      background: rgba(0, 229, 255, 0.1);
      border-radius: 8px;
      font-size: 11px;
      color: #00e5ff;
      font-weight: 600;
    }

    .loading {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 24px;
      z-index: 2000;
      text-align: center;
    }

    .loading .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid #ff69b4;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .version {
      position: absolute;
      bottom: 10px;
      left: 10px;
      color: rgba(255, 255, 255, 0.5);
      font-size: 10px;
      z-index: 1000;
    }
  </style>
</head>
<body>
  <div class="loading" id="loading">
    <div class="spinner"></div>
    <div>加载增强版爱心烟花游戏...</div>
  </div>

  <div class="instructions" id="instructions" style="display: none;">
    <h3>💗 爱心烟花射击游戏 💗</h3>

    <div class="section">
      <h4>🎯 游戏目标</h4>
      <div style="color: #87ceeb; margin-bottom: 10px;">
        用鼠标点击射击屏幕上的彩色气球，获得分数完成关卡！
      </div>
    </div>

    <div class="section">
      <h4>🎮 基础操作</h4>
      <div class="controls-grid">
        <div class="control-item">
          <span class="key">鼠标左键</span>
          <span>发射爱心烟花射击气球</span>
        </div>
        <div class="control-item">
          <span class="key">左键双击</span>
          <span>爆炸攻击（清理大片气球）</span>
        </div>
        <div class="control-item">
          <span class="key">I键</span>
          <span>隐藏/显示此说明</span>
        </div>
        <div class="control-item">
          <span class="key">空格键</span>
          <span>暂停/继续游戏</span>
        </div>
      </div>
    </div>

    <div class="section">
      <h4>🎈 气球类型</h4>
      <div class="feature-list">
        <div class="feature-item">
          <span class="feature-icon">🔵</span>
          <span>普通气球 - 基础分数</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">⭐</span>
          <span>特殊气球 - 高分奖励</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">👹</span>
          <span>Boss气球 - 需要多次射击</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">💥</span>
          <span>分裂气球 - 击中后分裂成小气球</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">🛡️</span>
          <span>护盾气球 - 需要先破坏护盾</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">⚡</span>
          <span>快速气球 - 移动速度很快</span>
        </div>
      </div>
    </div>

    <div class="section">
      <h4>💡 游戏技巧</h4>
      <div style="color: #98fb98; line-height: 1.6;">
        • <strong>连击奖励</strong>：连续击中气球可获得连击分数加成<br>
        • <strong>道具收集</strong>：击中道具获得特殊能力和分数加成<br>
        • <strong>任务系统</strong>：完成左侧显示的任务获得额外奖励<br>
        • <strong>爆炸攻击</strong>：左键双击触发爆炸攻击，有冷却时间，合理使用<br>
        • <strong>Boss战斗</strong>：Boss气球血量较高，需要多次射击<br>
        • <strong>关卡进度</strong>：击破足够气球即可进入下一关卡
      </div>
    </div>

    <div class="info">
      <strong style="color: #ff69b4;">🎯 开始游戏：</strong>
      <span style="color: #87ceeb;">直接用鼠标左键点击屏幕上的气球开始射击！</span><br>
      <strong style="color: #ff69b4;">💥 特殊技能：</strong>
      <span style="color: #87ceeb;">快速左键双击可触发爆炸攻击，清理大片气球！</span><br>
      <strong style="color: #ff69b4;">🏆 获胜条件：</strong>
      <span style="color: #87ceeb;">击破足够数量的气球，完成关卡任务即可过关！</span>
    </div>

    <div class="hide-tip">
      💡 按 I 键可隐藏/显示此面板
    </div>
  </div>

  <div class="version">Enhanced v2.0</div>

  <script>
    // 页面加载完成后隐藏加载界面
    window.addEventListener('load', function() {
      setTimeout(() => {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('instructions').style.display = 'block';
      }, 1500);
    });

    // 说明面板切换状态
    let instructionsVisible = true;

    // 只通过I键切换说明面板，移除点击切换功能
    document.addEventListener('keydown', function(e) {
      if (e.key === 'i' || e.key === 'I') {
        const instructions = document.getElementById('instructions');
        instructionsVisible = !instructionsVisible;
        instructions.style.transform = instructionsVisible ? 'translateY(0)' : 'translateY(100%)';
        instructions.style.opacity = instructionsVisible ? '0.95' : '0';
      }
    });
  </script>
  
  <script src="sketch.js"></script>
</body>
</html>
